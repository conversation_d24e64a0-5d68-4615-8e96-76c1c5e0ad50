import { useApi } from "~/services/useApi";

/**
 * Client-side API plugin
 * This plugin runs only on the client side and can be used to:
 * - Set up global API interceptors
 * - Configure authentication headers
 * - Add global error handling
 * - Set up API monitoring
 */

export default defineNuxtPlugin(() => {
  const { client } = useApi();

  // Add global request interceptor for authentication
  client.addRequestTransform((request) => {
    // Example: Add auth token from localStorage or cookie
    // const token = localStorage.getItem('auth-token')
    // if (token) {
    //   request.headers['Authorization'] = `Bearer ${token}`
    // }

    request.headers = request.headers || {};

    // Add request ID for tracking
    request.headers["X-Request-ID"] = crypto.randomUUID();

    // Add timestamp for debugging
    if (import.meta.dev) {
      request.headers["X-Request-Time"] = new Date().toISOString();
    }
  });

  // Add global response interceptor
  client.addResponseTransform((response) => {
    // Example: Handle global authentication errors
    if (response.status === 401) {
      // Clear auth state and redirect to login
      // clearAuthState()
      // navigateTo('/login')
      console.warn("Unauthorized request detected");
    }

    // Example: Handle rate limiting
    if (response.status === 429) {
      console.warn("Rate limit exceeded");
      // Could show a toast notification here
    }

    // Log slow requests in development
    if (import.meta.dev) {
      const requestTime = response.config?.headers?.["X-Request-Time"];
      if (requestTime) {
        const duration = Date.now() - new Date(requestTime).getTime();
        if (duration > 1000) {
          console.warn(
            `Slow API request: ${response.config?.url} took ${duration}ms`,
          );
        }
      }
    }
  });

  // Add global monitor for API analytics
  client.addMonitor((response) => {
    // Example: Send analytics data
    // analytics.track('api_request', {
    //   url: response.config?.url,
    //   method: response.config?.method,
    //   status: response.status,
    //   duration: response.duration
    // })

    if (import.meta.dev) {
      console.log("📊 API Monitor:", {
        url: response.config?.url,
        method: response.config?.method,
        status: response.status,
        ok: response.ok,
        problem: response.problem,
      });
    }
  });

  // Example: Set up periodic health check
  // if (import.meta.client) {
  //   // Check API health every 5 minutes
  //   setInterval(async () => {
  //     try {
  //       await client.get('/health')
  //       console.log('✅ API health check passed')
  //     } catch (error) {
  //       console.error('❌ API health check failed:', error)
  //       // Could trigger a notification or fallback behavior
  //     }
  //   }, 5 * 60 * 1000) // 5 minutes
  // }
});
